import { useContext } from "react";
import { UserContext } from "../context/UserContext";

export default function Chat() {
  const { allUser, getOtherUser } = useContext(UserContext);

  return (
    <div className="w-full h-screen flex items-center justify-center bg-gray-100">
      <div className="flex w-full max-w-4xl h-4/5 bg-white rounded-xl shadow-lg overflow-hidden">
        {/* User List Sidebar */}
        <div className="userList w-1/4 bg-gray-800 text-white p-4 overflow-y-auto">
          <h2 className="text-xl font-bold mb-4">Active Users</h2>
          {allUser.map((user) => (
            <div
              key={user._id}
              onClick={() => getOtherUser(user._id)}
              className="profile flex items-center mb-4 p-3 rounded-lg hover:bg-gray-700 transition cursor-pointer"
            >
              <div className="profileImage w-16 h-16 flex-shrink-0">
                <img
                  src={user.profile}
                  alt={user.fullName}
                  className="w-full h-full rounded-full object-cover border-2 border-blue-400"
                />
              </div>
              <div className="userInfo ml-3 overflow-hidden">
                <div className="fullName font-semibold truncate">
                  {user.fullName}
                </div>
                <div className="status text-green-400 text-sm flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Online
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="bg-gray-100 p-4 border-b flex items-center">
            <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
              <img
                src="https://cdn.pixabay.com/photo/2017/04/21/17/41/image-2249391_640.jpg"
                alt="Current chat"
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h3 className="font-bold">Sarah Johnson</h3>
              <p className="text-sm text-gray-500">Typing...</p>
            </div>
          </div>

          {/* Messages Container */}
          <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
            <div className="max-w-xs bg-white rounded-b-xl rounded-tr-xl p-3 mb-4">
              <p>Hey there! How are you doing?</p>
              <span className="text-xs text-gray-400 mt-1 block">10:42 AM</span>
            </div>

            <div className="max-w-xs ml-auto bg-blue-500 text-white rounded-b-xl rounded-tl-xl p-3 mb-4">
              <p>I'm good! Just working on this new chat UI</p>
              <span className="text-xs text-blue-100 mt-1 block">10:43 AM</span>
            </div>
          </div>

          {/* Message Input */}
          <div className="bg-gray-100 p-4">
            <div className="flex">
              <input
                type="text"
                placeholder="Type a message..."
                className="flex-1 rounded-l-lg py-3 px-4 border-t border-b border-l focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <button className="bg-blue-500 text-white px-6 rounded-r-lg font-medium hover:bg-blue-600 transition">
                Send
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
