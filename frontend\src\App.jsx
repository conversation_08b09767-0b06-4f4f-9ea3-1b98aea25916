import { BrowserRouter, Routes, Route } from "react-router";
import Chat from "./pages/Chat";
import { AuthProvider } from "./context/AuthContext";
import { UserProvider } from "./context/UserContext";
import { MessageProvider } from "./context/MessageContext";

export default function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <UserProvider>
          <MessageProvider>
            <Routes>
              <Route path="/" element={<Chat />} />
              <Route path="/profile/:userId" element={<Chat />} />
            </Routes>
          </MessageProvider>
        </UserProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}
