import { createContext } from "react";
import axios from "axios";

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const BACKEND_API = import.meta.env.VITE_API_URL;

  const register = async (userData) => {
    try {
      const response = await axios.post(
        `${BACKEND_API}/auth/register`,
        {
          userData,
        },
        {
          withCredentials: true,
        }
      );

      return response.data;
    } catch (error) {
      return error.response.data;
    }
  };

  const login = async (userData) => {
    try {
      const response = await axios.post(
        `${BACKEND_API}/auth/login`,
        {
          userData,
        },
        {
          withCredentials: true,
        }
      );

      return response.data;
    } catch (error) {
      return error.response.data;
    }
  };

  const logout = async () => {
    try {
      const response = await axios.post(
        `${BACKEND_API}/auth/logout`,
        {},
        {
          withCredentials: true,
        }
      );

      return response.data;
    } catch (error) {
      return error.response.data;
    }
  };

  return (
    <AuthContext.Provider value={{ register, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
