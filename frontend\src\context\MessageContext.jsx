import { createContext, useState, useEffect } from "react";
import axios from "axios";

export const MessageContext = createContext();

export const MessageProvider = ({ children }) => {
  const BACKEND_API = import.meta.env.VITE_API_URL;

  const [messages, setMessages] = useState();

  const sendMessage = async (receiver, content) => {
    try {
      const { data } = await axios.post(
        `${BACKEND_API}/message/send-message/${receiver}`,
        {
          content,
        },
        {
          withCredentials: true,
        }
      );

      setMessages((prevMessages) => [...prevMessages, data.data]);
    } catch (error) {
      return error.response.data;
    }
  };

  const getMessages = async (receiver) => {
    try {
      const { data } = await axios.get(
        `${BACKEND_API}/message/get-messages/${receiver}`,
        {
          withCredentials: true,
        }
      );

      setMessages(data.data);
    } catch (error) {
      return error.response.data;
    }
  };

  useEffect(() => {
    getMessages();
  }, []);

  return (
    <MessageContext.Provider value={{ getMessages, sendMessage, messages }}>
      {children}
    </MessageContext.Provider>
  );
};
